# Smart Auto Action Queue Implementation

## Summary

I've implemented a smart solution for the auto action queue scheduling issue. The problem was that the previous implementation used fixed cron patterns (`*/30 * * * *`) which caused all jobs to execute at the same time slots (23:30, 00:00, etc.), creating performance bottlenecks.

## Solution: Self-Scheduling with Load Distribution

### Key Changes:

1. **Replaced Cron Patterns with Self-Scheduling**
   - Each job schedules the next execution after completion
   - No more fixed time slots that cluster jobs together

2. **Smart Initial Scheduling**
   - First execution: 30 minutes from start time + random offset (0-5 minutes)
   - This distributes jobs across time instead of clustering them

3. **Load Distribution**
   - Random offset prevents all jobs from starting at the same time
   - Each job runs independently on its own 30-minute cycle

### Example Timeline:
- User starts auto mode at 23:25
- First execution: 23:55 + random offset (23:55-24:00)
- Next execution: 30 minutes later (00:25-00:30)
- Continues every 30 minutes from that user's specific start time

## Technical Implementation:

### 1. Updated Queue (`auto-action.queue.ts`)
- `addAutoActionJob()` now accepts optional `delayMs` parameter
- Calculates smart scheduling with random offset
- Removed dependency on repeatable job patterns

### 2. Updated Worker (`auto-action.worker.ts`)
- Added `scheduleNextExecution()` method
- Each job schedules its successor after completion
- Maintains 30-minute intervals per user

### 3. Updated Service (`bullmq-auto-action.service.ts`)
- Removed immediate execution on start
- First execution happens 30 minutes after starting auto mode
- Simplified service by moving execution logic to worker

## Benefits:

1. **Performance**: Distributes load across time instead of clustering
2. **Scalability**: Each user has their own schedule cycle
3. **Reliability**: Self-healing - jobs continue even after temporary failures
4. **Maintainability**: Simpler logic without complex cron patterns

## Testing:

To test the implementation:
1. Start auto mode for multiple users at different times
2. Observe that executions are distributed across time
3. Verify 30-minute intervals are maintained per user
4. Check that jobs continue after server restarts

The solution maintains the same 30-minute energy regeneration timing while solving the performance issue of clustered executions.
