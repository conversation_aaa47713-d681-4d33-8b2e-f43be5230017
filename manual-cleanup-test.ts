// Manual cleanup test - Add this to a controller for testing

import { Controller, Post, Get } from '@nestjs/common';
import { AutoActionQueue } from '../queue/auto-action.queue';

@Controller('admin/cleanup')
export class CleanupController {
  constructor(private readonly autoActionQueue: AutoActionQueue) {}

  @Get('repeatable-jobs')
  async getRepeatableJobs() {
    try {
      const jobs = await this.autoActionQueue.autoActionQueue.getRepeatableJobs();
      return {
        total: jobs.length,
        jobs: jobs.map(job => ({
          name: job.name,
          pattern: job.pattern,
          id: job.id,
          key: job.key,
        }))
      };
    } catch (error) {
      return { error: error.message };
    }
  }

  @Post('nuclear-cleanup')
  async nuclearCleanup() {
    try {
      const result = await this.autoActionQueue.obliterateAllRepeatableJobs();
      return {
        success: true,
        result
      };
    } catch (error) {
      return { 
        success: false, 
        error: error.message 
      };
    }
  }

  @Post('force-cleanup')
  async forceCleanup() {
    try {
      const result = await this.autoActionQueue.forceCleanupAllRepeatableJobs();
      return {
        success: true,
        result
      };
    } catch (error) {
      return { 
        success: false, 
        error: error.message 
      };
    }
  }

  @Post('remove-specific')
  async removeSpecific() {
    try {
      // Remove the specific job that's causing issues
      await this.autoActionQueue.removeAutoActionJob(25, '3', 'work' as any);
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error.message 
      };
    }
  }
}

// Alternative: Direct Redis cleanup script
export async function directRedisCleanup(autoActionQueue: AutoActionQueue) {
  try {
    console.log('Starting direct Redis cleanup...');
    
    // Get the Redis client
    const redis = (autoActionQueue as any).autoActionQueue.client;
    
    if (!redis) {
      throw new Error('Redis client not available');
    }

    // Get queue name
    const queueName = (autoActionQueue as any).autoActionQueue.name;
    console.log(`Queue name: ${queueName}`);

    // List all keys related to repeatable jobs
    const repeatableKeys = await redis.keys(`bull:${queueName}:repeat*`);
    console.log(`Found repeatable keys:`, repeatableKeys);

    // Delete all repeatable job keys
    if (repeatableKeys.length > 0) {
      const deleted = await redis.del(...repeatableKeys);
      console.log(`Deleted ${deleted} repeatable job keys`);
    }

    // Also check for specific job keys
    const jobKeys = await redis.keys(`bull:${queueName}:*repeat*`);
    console.log(`Found job keys:`, jobKeys);

    if (jobKeys.length > 0) {
      const deletedJobs = await redis.del(...jobKeys);
      console.log(`Deleted ${deletedJobs} job keys`);
    }

    console.log('Direct Redis cleanup completed');
    return { success: true, deletedKeys: repeatableKeys.length + jobKeys.length };
  } catch (error) {
    console.error('Direct Redis cleanup failed:', error);
    return { success: false, error: error.message };
  }
}
