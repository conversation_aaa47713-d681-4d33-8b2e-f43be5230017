import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { Job } from 'bullmq';
import { AutoActionJobData } from './dto/auto-action-job.dto';
import { AutoMode } from '../user/enums/auto-mode.enum';
import { UserService } from '../user/user.service';
import { EnergyService } from '../user/energy.service';
import { AutoActionQueue } from './auto-action.queue';
import { ModuleRef } from '@nestjs/core';


@Injectable()
@Processor('auto-actions')
export class AutoActionWorker extends WorkerHost {
  private readonly logger = new Logger(AutoActionWorker.name);
  private readonly MINIMUM_ENERGY_THRESHOLD = 10;

  constructor(
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    @Inject(forwardRef(() => EnergyService))
    private readonly energyService: EnergyService,
    private readonly autoActionQueue: AutoActionQueue,
    private readonly moduleRef: ModuleRef,
  ) {
    super();
  }

  async process(job: Job<AutoActionJobData>): Promise<void> {
    const { userId, targetId, type } = job.data;
    const jobName = `${userId}_${targetId}_${type}`;

    this.logger.log(`Processing auto action job: ${jobName}`);

    try {
      // Check if user still exists and is premium
      const user = await this.userService.findOne(userId);
      if (!user) {
        this.logger.warn(`User ${userId} not found, removing job: ${jobName}`);
        await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);
        return;
      }

      if (!user.isPremium) {
        this.logger.warn(`User ${userId} is no longer premium, removing job: ${jobName}`);
        await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);
        return;
      }

      // Check if auto mode is still active for this user
      if (user.activeAutoMode !== type || user.autoTargetId !== targetId) {
        this.logger.log(`Auto mode changed for user ${userId}, removing job: ${jobName}`);

        // Try to remove the job, but don't fail if it can't be removed immediately
        try {
          await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);
        } catch (removeError) {
          this.logger.warn(`Could not remove job ${jobName} immediately: ${removeError.message}. Job will stop executing.`);
        }

        return;
      }

      // Check if auto mode has expired (for work mode)
      if (type === AutoMode.WORK && user.autoModeExpiresAt && new Date() > user.autoModeExpiresAt) {
        this.logger.log(`Auto work for user ${userId} has expired, removing job: ${jobName}`);
        await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);
        await this.userService.updateAutoMode(userId, {
          activeAutoMode: AutoMode.NONE,
          autoTargetId: null,
          autoModeExpiresAt: null,
        });
        return;
      }

      // Calculate current energy before execution
      const energyBefore = this.energyService.calculateCurrentEnergy(user);

      // Check if user has minimum required energy
      if (energyBefore < this.MINIMUM_ENERGY_THRESHOLD) {
        this.logger.log(`User ${userId} has insufficient energy (${energyBefore}), skipping execution`);
        return;
      }

      // Execute the appropriate action
      let actionExecuted = false;
      if (type === AutoMode.WORK) {
        actionExecuted = await this.executeAutoWork(userId, targetId, energyBefore);
      } else if (type === AutoMode.WAR) {
        actionExecuted = await this.executeAutoWar(userId, targetId, energyBefore);
      }

      // If action was executed successfully, check if energy was consumed
      if (actionExecuted) {
        await this.checkEnergyConsumptionAndRetry(userId, targetId, type, energyBefore, jobName);
      }

      this.logger.log(`Successfully processed auto ${type} for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error processing auto action job: ${jobName}`, error);

      // If it's a critical error (like target not found), remove the job
      if (this.isCriticalError(error)) {
        this.logger.warn(`Critical error detected, removing job: ${jobName}`);
        await this.autoActionQueue.removeAutoActionJob(userId, targetId, type);
        return; // Don't schedule next execution for critical errors
      }

      throw error; // Re-throw to trigger retry mechanism
    } finally {
      // ALWAYS try to schedule the next execution in the finally block
      // This ensures auto mode continues even if there were errors or failures
      await this.scheduleNextExecutionWithFallback(userId, targetId, type, jobName);
    }
  }

  private async executeAutoWork(userId: number, factoryId: string, energyAmount: number): Promise<boolean> {
    try {
      this.logger.log(`Executing auto work for user ${userId} at factory ${factoryId} with ${energyAmount} energy`);

      // Dynamically resolve FactoryService to avoid circular dependency
      const { FactoryService } = await import('../factory/factory.service');
      const factoryService = this.moduleRef.get(FactoryService, { strict: false });

      if (!factoryService) {
        throw new Error('FactoryService not available');
      }

      // Work at the factory
      await factoryService.workAtFactory(+factoryId, userId, {
        energySpent: energyAmount,
      });

      this.logger.log(`Auto work successful for user ${userId} at factory ${factoryId}`);
      return true; // Action was executed successfully
    } catch (error) {
      this.logger.error(`Auto work failed for user ${userId} at factory ${factoryId}: ${error.message}`);
      throw error;
    }
  }

  private async executeAutoWar(userId: number, warId: string, energyAmount: number): Promise<boolean> {
    try {
      this.logger.log(`Executing auto war attack for user ${userId} in war ${warId} with ${energyAmount} energy`);

      // Dynamically resolve WarService to avoid circular dependency
      const { WarService } = await import('../war/war.service');
      const warService = this.moduleRef.get(WarService, { strict: false });

      if (!warService) {
        throw new Error('WarService not available');
      }

      // Check if war is still active (GROUND_PHASE or REVOLUTION_PHASE are active phases)
      const war = await warService.findWarById(warId);
      if (!war || !warService.isWarActive(war)) {
        this.logger.log(`War ${warId} is no longer active (status: ${war?.status}), stopping auto attack for user ${userId}`);
        await this.autoActionQueue.removeAutoActionJob(userId, warId, AutoMode.WAR);
        await this.userService.updateAutoMode(userId, {
          activeAutoMode: AutoMode.NONE,
          autoTargetId: null,
          autoModeExpiresAt: null,
        });
        return false; // War ended, no action executed
      }

      // For revolution wars, we need to determine which side the user is on
      // by checking their existing participation
      let userSide: 'attacker' | 'defender' | undefined;

      if (war && war.warType === 'revolution') {
        // Check if user is in attackers list
        const isInAttackers = war.participants?.attackers?.some(p => p.userId === userId);
        // Check if user is in defenders list
        const isInDefenders = war.participants?.defenders?.some(p => p.userId === userId);

        if (isInAttackers) {
          userSide = 'attacker';
        } else if (isInDefenders) {
          userSide = 'defender';
        } else {
          // User is not in either list - this could happen if they haven't participated yet
          // Let the participateInWar method handle it (it will require a side to be specified)
          this.logger.warn(`User ${userId} not found in revolution war ${warId} participants. Auto attack may fail.`);
        }
      }

      // Create participation DTO for the war service
      const participateDto = {
        energyAmount: energyAmount,
        autoMode: false, // Don't trigger auto mode recursively
        autoEnergyPercentage: 100,
        side: userSide, // Include side for revolution wars
      };

      // Participate in the war (correct parameter order: userId, warId, dto)
      await warService.participateInWar(userId, warId, participateDto);

      this.logger.log(`Auto war attack successful for user ${userId} in war ${warId}`);
      return true; // Action was executed successfully
    } catch (error) {
      this.logger.error(`Auto war attack failed for user ${userId} in war ${warId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Check if energy was consumed and schedule retry if not
   */
  private async checkEnergyConsumptionAndRetry(
    userId: number,
    targetId: string,
    type: AutoMode,
    energyBefore: number,
    jobName: string,
  ): Promise<void> {
    try {
      // Wait a moment for the database to update
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Get updated user data
      const updatedUser = await this.userService.findOne(userId);
      const energyAfter = this.energyService.calculateCurrentEnergy(updatedUser);

      this.logger.log(`Energy check for user ${userId}: before=${energyBefore}, after=${energyAfter}`);

      // Check if energy was actually consumed
      if (energyAfter >= energyBefore) {
        this.logger.warn(`Energy was not consumed for user ${userId} (before: ${energyBefore}, after: ${energyAfter}). Scheduling retry...`);

        // Schedule a retry in 2 minutes instead of waiting for the next 30-minute cycle
        // This is only for error recovery, not continuous execution
        await this.scheduleRetryExecution(userId, targetId, type, jobName);
      } else {
        this.logger.log(`Energy successfully consumed for user ${userId} (${energyBefore} -> ${energyAfter})`);
        // No additional retry needed - the regular 30-minute cycle will handle the next execution
        // This maintains the proper energy regeneration timing (30 minutes)
      }
    } catch (error) {
      this.logger.error(`Error checking energy consumption for user ${userId}: ${error.message}`);
    }
  }

  /**
   * Schedule the next execution with comprehensive fallback mechanisms
   * This ensures auto mode continues even if scheduling fails
   */
  private async scheduleNextExecutionWithFallback(
    userId: number,
    targetId: string,
    type: AutoMode,
    jobName: string,
  ): Promise<void> {
    const maxRetries = 3;
    let attempt = 0;

    while (attempt < maxRetries) {
      try {
        attempt++;

        // Check if user still has auto mode enabled
        const user = await this.userService.findOne(userId);
        if (!user || user.activeAutoMode !== type || user.autoTargetId !== targetId) {
          this.logger.log(`User ${userId} auto mode changed, not scheduling next execution`);
          return;
        }

        // Check if auto mode has expired (for work mode)
        if (type === AutoMode.WORK && user.autoModeExpiresAt && new Date() > user.autoModeExpiresAt) {
          this.logger.log(`Auto work for user ${userId} has expired, not scheduling next execution`);
          await this.userService.updateAutoMode(userId, {
            activeAutoMode: AutoMode.NONE,
            autoTargetId: null,
            autoModeExpiresAt: null,
          });
          return;
        }

        // Schedule next execution in 30 minutes
        const delayMs = 30 * 60 * 1000; // 30 minutes
        await this.autoActionQueue.addAutoActionJob(userId, targetId, type, delayMs);

        this.logger.log(`Scheduled next auto ${type} execution for user ${userId} in 30 minutes (attempt ${attempt})`);
        return; // Success, exit the retry loop

      } catch (error) {
        this.logger.error(`Failed to schedule next execution for user ${userId} (attempt ${attempt}/${maxRetries}): ${error.message}`);

        if (attempt === maxRetries) {
          // All retries failed - implement emergency fallback
          this.logger.error(`All scheduling attempts failed for ${jobName}. Implementing emergency fallback...`);
          await this.implementEmergencyFallback(userId, targetId, type, jobName);
        } else {
          // Wait before retrying (exponential backoff)
          const backoffMs = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
          await new Promise(resolve => setTimeout(resolve, backoffMs));
        }
      }
    }
  }

  /**
   * Emergency fallback mechanism when all scheduling attempts fail
   * This ensures auto mode doesn't get stuck even in worst-case scenarios
   */
  private async implementEmergencyFallback(
    userId: number,
    targetId: string,
    type: AutoMode,
    jobName: string,
  ): Promise<void> {
    try {
      this.logger.warn(`Implementing emergency fallback for ${jobName}`);

      // Fallback 1: Try using retry job mechanism with longer delay
      try {
        const emergencyDelayMs = 35 * 60 * 1000; // 35 minutes (slightly longer than normal)
        await this.autoActionQueue.addRetryJob(userId, targetId, type, emergencyDelayMs);
        this.logger.log(`Emergency fallback: Scheduled retry job for ${jobName} in 35 minutes`);
        return;
      } catch (retryError) {
        this.logger.error(`Emergency retry job failed for ${jobName}: ${retryError.message}`);
      }

      // Fallback 2: Mark for restoration by the cron job
      // The BullMQAutoActionService has a cron job that restores missing auto actions
      this.logger.warn(
        `All fallback mechanisms failed for ${jobName}. ` +
        `Auto mode will be restored by the next cron job execution (every 30 minutes). ` +
        `User auto mode settings remain active in database.`
      );

    } catch (error) {
      this.logger.error(`Emergency fallback failed for ${jobName}: ${error.message}`);
      // At this point, the cron job restoration is our last resort
    }
  }

  /**
   * Schedule a retry execution
   */
  private async scheduleRetryExecution(
    userId: number,
    targetId: string,
    type: AutoMode,
    jobName: string,
    delayMs: number = 120000, // Default 2 minutes
  ): Promise<void> {
    try {
      this.logger.log(`Scheduling retry execution for ${jobName} in ${delayMs / 1000} seconds`);

      // Use the queue to add a delayed job
      await this.autoActionQueue.addRetryJob(userId, targetId, type, delayMs);

    } catch (error) {
      this.logger.error(`Failed to schedule retry for ${jobName}: ${error.message}`);
    }
  }

  private isCriticalError(error: any): boolean {
    const criticalMessages = [
      'Factory not found',
      'War not found',
      'User not found',
      'Factory does not exist',
      'War does not exist',
      'User is not in the same region',
      'Factory is not in user region',
      'FactoryService not available',
      'WarService not available',
      'Can only participate in wars during the ground phase',
      'War is already ended',
      'Factory is not in user region',
      'You can only participate in wars involving your region',
      'You are already traveling',
      'Auto mode is only available for premium users',
    ];

    return criticalMessages.some(message =>
      error.message && error.message.includes(message)
    );
  }
}
