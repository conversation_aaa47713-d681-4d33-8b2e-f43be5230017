import { AutoMode } from '../../user/enums/auto-mode.enum';

export interface AutoActionJobData {
  userId: number;
  targetId: string;
  type: AutoMode;
}

export interface AutoActionJobOptions {
  repeat?: {
    pattern?: string; // Cron pattern (legacy)
    every?: number; // Interval in milliseconds
    immediately?: boolean; // Whether to run immediately
  };
  delay?: number; // Delay before first execution
  removeOnComplete?: number;
  removeOnFail?: number;
  attempts?: number;
  backoff?: {
    type: string;
    delay: number;
  };
}
