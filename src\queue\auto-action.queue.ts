import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { AutoMode } from '../user/enums/auto-mode.enum';
import { AutoActionJobData, AutoActionJobOptions } from './dto/auto-action-job.dto';

@Injectable()
export class AutoActionQueue {
  private readonly logger = new Logger(AutoActionQueue.name);

  constructor(
    @InjectQueue('auto-actions')
    private readonly autoActionQueue: Queue<AutoActionJobData>,
  ) {}

  /**
   * Add a new auto action job to the queue
   * Uses self-scheduling approach: each job schedules the next one after completion
   * Includes race condition protection and duplicate prevention
   */
  async addAutoActionJob(
    userId: number,
    targetId: string,
    type: AutoMode,
    delayMs?: number,
  ): Promise<void> {
    // Validate input parameters
    if (!userId || !targetId || !type) {
      throw new Error('Invalid parameters: userId, targetId, and type are required');
    }

    if (typeof userId !== 'number' || userId <= 0) {
      throw new Error('Invalid userId: must be a positive number');
    }

    if (!Object.values(AutoMode).includes(type)) {
      throw new Error(`Invalid auto mode type: ${type}`);
    }

    const jobName = `${userId}_${targetId}_${type}`;
    const jobData: AutoActionJobData = {
      userId,
      targetId,
      type,
    };

    // Calculate delay for execution
    let executionDelay = delayMs;
    if (executionDelay === undefined) {
      // For initial job, schedule exactly 30 minutes from now
      // The random distribution happens at the user level (when they start auto mode)
      // not at the job scheduling level to maintain precise 30-minute intervals
      executionDelay = 30 * 60 * 1000; // Exactly 30 minutes
    }

    // Validate delay
    if (executionDelay < 0) {
      throw new Error('Invalid delay: must be non-negative');
    }

    const nextExecutionTime = new Date(Date.now() + executionDelay);

    const jobOptions: AutoActionJobOptions = {
      delay: executionDelay,
      removeOnComplete: 10, // Keep last 10 completed jobs
      removeOnFail: 50, // Keep last 50 failed jobs
      attempts: 3, // Retry up to 3 times on failure
      backoff: {
        type: 'exponential',
        delay: 1000, // Start with 1 second delay
      },
      // Add job ID to prevent duplicates
      jobId: `${jobName}_${Date.now()}`,
    };

    try {
      // Check for existing jobs to prevent duplicates (race condition protection)
      const existingJobs = await this.autoActionQueue.getJobs(['waiting', 'delayed']);
      const duplicateJob = existingJobs.find(job =>
        job.name === jobName &&
        job.opts?.delay &&
        job.opts.delay > 0 &&
        !job.name.includes('_retry_')
      );

      if (duplicateJob) {
        this.logger.warn(`Duplicate job detected for ${jobName}, skipping creation`);
        return;
      }

      await this.autoActionQueue.add(jobName, jobData, jobOptions);
      this.logger.log(
        `Added auto action job: ${jobName} - scheduled for ${nextExecutionTime.toISOString()}`
      );
    } catch (error) {
      this.logger.error(`Failed to add auto action job: ${jobName}`, error);
      throw error;
    }
  }

  /**
   * Add a one-time retry job with delay
   */
  async addRetryJob(
    userId: number,
    targetId: string,
    type: AutoMode,
    delayMs: number,
  ): Promise<void> {
    const retryJobName = `${userId}_${targetId}_${type}_retry_${Date.now()}`;
    const jobData: AutoActionJobData = {
      userId,
      targetId,
      type,
    };

    const jobOptions = {
      delay: delayMs, // Delay before execution
      removeOnComplete: 5, // Keep fewer completed retry jobs
      removeOnFail: 10, // Keep fewer failed retry jobs
      attempts: 1, // Don't retry the retry job
    };

    try {
      await this.autoActionQueue.add(retryJobName, jobData, jobOptions);
      this.logger.log(`Added retry job: ${retryJobName} with ${delayMs}ms delay`);
    } catch (error) {
      this.logger.error(`Failed to add retry job: ${retryJobName}`, error);
      throw error;
    }
  }

  /**
   * Remove an auto action job from the queue
   * Handles both new self-scheduling jobs and old repeatable jobs
   */
  async removeAutoActionJob(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<void> {
    const jobName = `${userId}_${targetId}_${type}`;

    try {
      let removedJobs = 0;

      // First, try to remove any old repeatable jobs with this name
      try {
        const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
        for (const repeatableJob of repeatableJobs) {
          if (repeatableJob.name === jobName) {
            this.logger.log(`Attempting to remove repeatable job: ${repeatableJob.name} with pattern: ${repeatableJob.pattern}`);

            // Try multiple removal strategies
            const removalStrategies = [
              // Strategy 1: Use exact job details
              () => this.autoActionQueue.removeRepeatable(
                repeatableJob.name,
                { pattern: repeatableJob.pattern },
                repeatableJob.id || undefined
              ),
              // Strategy 2: Use common patterns
              () => this.autoActionQueue.removeRepeatable(
                repeatableJob.name,
                { pattern: '*/30 * * * *' },
                repeatableJob.id || undefined
              ),
              // Strategy 3: Use alternative pattern
              () => this.autoActionQueue.removeRepeatable(
                repeatableJob.name,
                { pattern: '0,30 * * * *' },
                repeatableJob.id || undefined
              ),
              // Strategy 4: Without job ID
              () => this.autoActionQueue.removeRepeatable(
                repeatableJob.name,
                { pattern: repeatableJob.pattern || '*/30 * * * *' }
              ),
            ];

            let removed = false;
            for (let i = 0; i < removalStrategies.length && !removed; i++) {
              try {
                removed = await removalStrategies[i]();
                if (removed) {
                  removedJobs++;
                  this.logger.log(`Successfully removed repeatable job: ${repeatableJob.name} using strategy ${i + 1}`);
                  break;
                }
              } catch (strategyError) {
                this.logger.warn(`Strategy ${i + 1} failed for ${repeatableJob.name}: ${strategyError.message}`);
              }
            }

            if (!removed) {
              this.logger.error(`All removal strategies failed for repeatable job: ${repeatableJob.name}`);
              // Try to get more details about the job
              this.logger.error(`Job details: pattern=${repeatableJob.pattern}, id=${repeatableJob.id}, key=${repeatableJob.key}`);

              // Last resort: try to remove using the job key directly
              try {
                if (repeatableJob.key) {
                  // Try to remove the repeatable job using its key
                  const keyRemoved = await this.autoActionQueue.removeRepeatable(
                    repeatableJob.name,
                    { pattern: repeatableJob.pattern || '*/30 * * * *' },
                    repeatableJob.key
                  );
                  if (keyRemoved) {
                    removedJobs++;
                    this.logger.log(`Successfully removed repeatable job using key: ${repeatableJob.name}`);
                    removed = true;
                  }
                }
              } catch (keyError) {
                this.logger.error(`Key-based removal also failed for ${repeatableJob.name}: ${keyError.message}`);
              }
            }
          }
        }
      } catch (repeatableError) {
        this.logger.warn(`Failed to get repeatable jobs: ${repeatableError.message}`);
      }

      // Then, remove regular jobs (waiting, delayed, active)
      const jobs = await this.autoActionQueue.getJobs(['waiting', 'delayed', 'active']);
      for (const job of jobs) {
        try {
          if (job.name === jobName || job.name.startsWith(`${jobName}_retry_`)) {
            // Check if this is a repeatable job instance (these can't be removed directly)
            if (job.opts?.repeat) {
              this.logger.log(`Skipping repeatable job instance: ${job.name} (will be handled by removeRepeatable)`);
              continue;
            }

            await job.remove();
            removedJobs++;
            this.logger.log(`Removed job: ${job.name} (ID: ${job.id})`);
          }
        } catch (jobRemovalError) {
          if (jobRemovalError.message?.includes('belongs to a job scheduler')) {
            this.logger.log(`Skipping scheduler-managed job: ${job.name}`);
          } else {
            this.logger.error(`Failed to remove individual job ${job.name}: ${jobRemovalError.message}`);
          }
        }
      }

      this.logger.log(`Removed auto action job: ${jobName} (${removedJobs} jobs removed)`);
    } catch (error) {
      this.logger.error(`Failed to remove auto action job: ${jobName}`, error);
      throw error;
    }
  }

  /**
   * Parse and validate job name components
   * Returns null if job name is invalid
   */
  private parseJobName(jobName: string): { userId: number; targetId: string; type: AutoMode } | null {
    try {
      // Skip retry jobs
      if (jobName.includes('_retry_')) {
        return null;
      }

      const parts = jobName.split('_');
      if (parts.length < 3) {
        this.logger.warn(`Invalid job name format: ${jobName} (expected at least 3 parts)`);
        return null;
      }

      const [userIdStr, targetId, type] = parts;

      // Validate userId
      const userId = parseInt(userIdStr);
      if (isNaN(userId) || userId <= 0) {
        this.logger.warn(`Invalid userId in job name: ${jobName} (userId: ${userIdStr})`);
        return null;
      }

      // Validate targetId
      if (!targetId || targetId.trim() === '') {
        this.logger.warn(`Invalid targetId in job name: ${jobName} (targetId: ${targetId})`);
        return null;
      }

      // Validate type
      if (!Object.values(AutoMode).includes(type as AutoMode)) {
        this.logger.warn(`Invalid auto mode type in job name: ${jobName} (type: ${type})`);
        return null;
      }

      return {
        userId,
        targetId,
        type: type as AutoMode,
      };
    } catch (error) {
      this.logger.error(`Error parsing job name: ${jobName}`, error);
      return null;
    }
  }

  /**
   * Get all active auto action jobs
   * Since we use self-scheduling, check for waiting/delayed jobs
   */
  async getActiveJobs(): Promise<Array<{ userId: number; targetId: string; type: AutoMode }>> {
    try {
      const jobs = await this.autoActionQueue.getJobs(['waiting', 'delayed']);
      const activeJobs: Array<{ userId: number; targetId: string; type: AutoMode }> = [];

      for (const job of jobs) {
        const parsed = this.parseJobName(job.name);
        if (parsed) {
          activeJobs.push(parsed);
        }
      }

      return activeJobs;
    } catch (error) {
      this.logger.error('Failed to get active jobs', error);
      return [];
    }
  }

  /**
   * Get all active auto action jobs with user validation
   * This method checks if the jobs match the user's current auto mode settings
   */
  async getActiveJobsWithValidation(userService: any): Promise<Array<{
    userId: number;
    targetId: string;
    type: AutoMode;
    isValid: boolean;
    userAutoMode?: AutoMode;
    userTargetId?: string;
  }>> {
    try {
      const jobs = await this.autoActionQueue.getJobs(['waiting', 'delayed']);
      const jobsWithValidation: Array<{
        userId: number;
        targetId: string;
        type: AutoMode;
        isValid: boolean;
        userAutoMode?: AutoMode;
        userTargetId?: string;
      }> = [];

      for (const job of jobs) {
        const parsed = this.parseJobName(job.name);
        if (!parsed) {
          continue;
        }

        const { userId, targetId, type } = parsed;

        // Get user's current auto mode settings
        const user = await userService.findOne(userId);
        const isValid = user &&
          user.activeAutoMode === type &&
          user.autoTargetId === targetId;

        jobsWithValidation.push({
          userId,
          targetId,
          type,
          isValid: !!isValid,
          userAutoMode: user?.activeAutoMode,
          userTargetId: user?.autoTargetId,
        });

        // If job is invalid, log and optionally remove it
        if (!isValid) {
          this.logger.warn(
            `Found inconsistent job: ${job.name}. ` +
            `User auto mode: ${user?.activeAutoMode}, target: ${user?.autoTargetId}. ` +
            `Job will be marked for cleanup.`
          );
        }
      }

      return jobsWithValidation;
    } catch (error) {
      this.logger.error('Failed to get active jobs with validation', error);
      return [];
    }
  }

  /**
   * Clean up inconsistent jobs that don't match user's current auto mode settings
   */
  async cleanupInconsistentJobs(userService: any): Promise<{
    totalJobs: number;
    inconsistentJobs: number;
    cleanedJobs: number;
    errors: string[];
  }> {
    const result = {
      totalJobs: 0,
      inconsistentJobs: 0,
      cleanedJobs: 0,
      errors: [] as string[],
    };

    try {
      const jobsWithValidation = await this.getActiveJobsWithValidation(userService);
      result.totalJobs = jobsWithValidation.length;

      const inconsistentJobs = jobsWithValidation.filter(job => !job.isValid);
      result.inconsistentJobs = inconsistentJobs.length;

      this.logger.log(
        `Found ${result.inconsistentJobs} inconsistent jobs out of ${result.totalJobs} total jobs`
      );

      for (const job of inconsistentJobs) {
        try {
          await this.removeAutoActionJob(job.userId, job.targetId, job.type);
          result.cleanedJobs++;
          this.logger.log(
            `Cleaned up inconsistent job: ${job.userId}_${job.targetId}_${job.type} ` +
            `(user mode: ${job.userAutoMode}, user target: ${job.userTargetId})`
          );
        } catch (error) {
          const errorMsg = `Failed to clean up job ${job.userId}_${job.targetId}_${job.type}: ${error.message}`;
          result.errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      this.logger.log(
        `Cleanup completed: ${result.cleanedJobs}/${result.inconsistentJobs} inconsistent jobs removed`
      );

      return result;
    } catch (error) {
      this.logger.error('Failed to cleanup inconsistent jobs', error);
      result.errors.push(`Cleanup failed: ${error.message}`);
      return result;
    }
  }

  /**
   * Check if a specific auto action job exists
   * Checks both new self-scheduling jobs and old repeatable jobs
   */
  async jobExists(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<boolean> {
    const jobName = `${userId}_${targetId}_${type}`;

    try {
      // Check for old repeatable jobs first
      try {
        const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
        const repeatableExists = repeatableJobs.some(job => job.name === jobName);
        if (repeatableExists) {
          this.logger.log(`Found existing repeatable job: ${jobName}`);
          return true;
        }
      } catch (repeatableError) {
        this.logger.warn(`Failed to check repeatable jobs: ${repeatableError.message}`);
      }

      // Check for any pending jobs with this name
      const pendingJobs = await this.autoActionQueue.getJobs(['waiting', 'delayed', 'active']);
      const pendingExists = pendingJobs.some(job => job.name === jobName && !job.name.includes('_retry_'));

      if (pendingExists) {
        this.logger.log(`Found existing job: ${jobName}`);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Failed to check if job exists: ${jobName}`, error);
      return false;
    }
  }

  /**
   * Force remove a job by trying all possible removal methods
   * This is a more aggressive approach for stuck jobs
   */
  async forceRemoveAutoActionJob(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<{ success: boolean; methods: string[] }> {
    const jobName = `${userId}_${targetId}_${type}`;
    const result = { success: false, methods: [] as string[] };

    this.logger.log(`Force removing job: ${jobName}`);

    try {
      // Method 1: Regular removal
      try {
        await this.removeAutoActionJob(userId, targetId, type);
        result.methods.push('regular_removal');
        result.success = true;
        return result;
      } catch (error) {
        this.logger.warn(`Regular removal failed for ${jobName}: ${error.message}`);
      }

      // Method 2: Remove all jobs (waiting, delayed, active) with this name
      try {
        const allJobs = await this.autoActionQueue.getJobs(['waiting', 'delayed', 'active', 'completed', 'failed']);
        let removedCount = 0;
        for (const job of allJobs) {
          if (job.name === jobName || job.name.startsWith(`${jobName}_retry_`)) {
            try {
              await job.remove();
              removedCount++;
            } catch (jobError) {
              // Ignore individual job removal errors
            }
          }
        }
        if (removedCount > 0) {
          result.methods.push(`individual_jobs_${removedCount}`);
          result.success = true;
        }
      } catch (error) {
        this.logger.warn(`Individual job removal failed for ${jobName}: ${error.message}`);
      }

      this.logger.log(`Force removal result for ${jobName}: success=${result.success}, methods=[${result.methods.join(', ')}]`);
      return result;
    } catch (error) {
      this.logger.error(`Force removal failed for ${jobName}`, error);
      return result;
    }
  }

  /**
   * Clean up all travel auto jobs (travel should not be repeating)
   */
  async cleanupTravelAutoJobs(): Promise<{
    totalTravelJobs: number;
    removedJobs: number;
    errors: string[];
  }> {
    const result = {
      totalTravelJobs: 0,
      removedJobs: 0,
      errors: [] as string[],
    };

    try {
      const jobs = await this.autoActionQueue.getJobs(['waiting', 'delayed', 'active']);
      const travelJobs = jobs.filter(job => job.name.endsWith('_travel'));
      result.totalTravelJobs = travelJobs.length;

      this.logger.log(`Found ${result.totalTravelJobs} travel auto jobs to clean up`);

      for (const job of travelJobs) {
        try {
          const [userId, targetId] = job.name.split('_');
          await this.removeAutoActionJob(parseInt(userId), targetId, AutoMode.TRAVEL);
          result.removedJobs++;
          this.logger.log(`Cleaned up travel auto job: ${job.name}`);
        } catch (error) {
          const errorMsg = `Failed to clean up travel job ${job.name}: ${error.message}`;
          result.errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      this.logger.log(
        `Travel auto job cleanup completed: ${result.removedJobs}/${result.totalTravelJobs} jobs removed`
      );

      return result;
    } catch (error) {
      this.logger.error('Failed to cleanup travel auto jobs', error);
      result.errors.push(`Cleanup failed: ${error.message}`);
      return result;
    }
  }

  /**
   * Clean up old repeatable jobs from previous implementation
   */
  async cleanupOldRepeatableJobs(): Promise<{
    totalJobs: number;
    removedJobs: number;
    errors: string[];
  }> {
    const result = {
      totalJobs: 0,
      removedJobs: 0,
      errors: [] as string[],
    };

    try {
      // Get all repeatable jobs and remove them since we now use self-scheduling
      const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
      result.totalJobs = repeatableJobs.length;

      if (repeatableJobs.length === 0) {
        this.logger.log('No old repeatable jobs found to clean up');
        return result;
      }

      this.logger.log(`Found ${repeatableJobs.length} old repeatable jobs to clean up`);

      for (const job of repeatableJobs) {
        try {
          // Try to remove the repeatable job
          const removed = await this.autoActionQueue.removeRepeatable(
            job.name,
            { pattern: job.pattern || '*/30 * * * *' },
            job.id || undefined
          );

          if (removed) {
            result.removedJobs++;
            this.logger.log(`Removed old repeatable job: ${job.name}`);
          } else {
            this.logger.warn(`Failed to remove old repeatable job: ${job.name} (removeRepeatable returned false)`);
          }
        } catch (error) {
          const errorMsg = `Failed to remove old repeatable job ${job.name}: ${error.message}`;
          result.errors.push(errorMsg);
          this.logger.warn(errorMsg);
        }
      }

      this.logger.log(`Old repeatable jobs cleanup completed: ${result.removedJobs}/${result.totalJobs} jobs removed`);
      return result;
    } catch (error) {
      this.logger.error('Failed to cleanup old repeatable jobs', error);
      result.errors.push(`Cleanup failed: ${error.message}`);
      return result;
    }
  }

  /**
   * Nuclear option: Obliterate all repeatable jobs by clearing Redis keys
   */
  async obliterateAllRepeatableJobs(): Promise<{
    totalJobs: number;
    removedJobs: number;
    errors: string[];
  }> {
    const result = {
      totalJobs: 0,
      removedJobs: 0,
      errors: [] as string[],
    };

    try {
      this.logger.warn('NUCLEAR OPTION: Obliterating ALL repeatable jobs...');

      // Get all repeatable jobs first
      const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
      result.totalJobs = repeatableJobs.length;

      if (repeatableJobs.length === 0) {
        this.logger.log('No repeatable jobs found to obliterate');
        return result;
      }

      this.logger.warn(`Obliterating ${repeatableJobs.length} repeatable jobs...`);

      // Method 1: Try to remove each job individually with all possible combinations
      for (const job of repeatableJobs) {
        let removed = false;

        // Try all possible removal combinations
        const removalAttempts = [
          // With exact details
          { name: job.name, opts: { pattern: job.pattern }, id: job.id },
          { name: job.name, opts: { pattern: job.pattern }, id: job.key },
          { name: job.name, opts: { pattern: job.pattern }, id: undefined },

          // With common patterns
          { name: job.name, opts: { pattern: '*/30 * * * *' }, id: job.id },
          { name: job.name, opts: { pattern: '*/30 * * * *' }, id: job.key },
          { name: job.name, opts: { pattern: '*/30 * * * *' }, id: undefined },

          // With alternative patterns
          { name: job.name, opts: { pattern: '0,30 * * * *' }, id: job.id },
          { name: job.name, opts: { pattern: '0,30 * * * *' }, id: job.key },
          { name: job.name, opts: { pattern: '0,30 * * * *' }, id: undefined },
        ];

        for (const attempt of removalAttempts) {
          if (removed) break;

          try {
            removed = await this.autoActionQueue.removeRepeatable(
              attempt.name,
              attempt.opts,
              attempt.id || undefined
            );

            if (removed) {
              result.removedJobs++;
              this.logger.log(`Obliterated repeatable job: ${job.name}`);
              break;
            }
          } catch (error) {
            // Continue to next attempt
          }
        }

        if (!removed) {
          result.errors.push(`Failed to obliterate job: ${job.name}`);
          this.logger.error(`Failed to obliterate repeatable job: ${job.name}`);
        }
      }

      // Method 2: If individual removal failed, try to clear the entire repeatable jobs set
      if (result.removedJobs < result.totalJobs) {
        try {
          this.logger.warn('Individual removal incomplete, attempting Redis key cleanup...');

          // Get the Redis client from the queue
          const redis = (this.autoActionQueue as any).client;
          if (redis) {
            // Clear the repeatable jobs key
            const queueName = this.autoActionQueue.name;
            const repeatableKey = `bull:${queueName}:repeat`;

            const deleted = await redis.del(repeatableKey);
            if (deleted > 0) {
              this.logger.warn(`Cleared Redis repeatable jobs key: ${repeatableKey}`);
              result.removedJobs = result.totalJobs; // Assume all were removed
            }
          }
        } catch (redisError) {
          result.errors.push(`Redis cleanup failed: ${redisError.message}`);
          this.logger.error(`Redis cleanup failed: ${redisError.message}`);
        }
      }

      this.logger.warn(`Obliteration completed: ${result.removedJobs}/${result.totalJobs} repeatable jobs removed`);
      return result;
    } catch (error) {
      this.logger.error('Failed to obliterate repeatable jobs', error);
      result.errors.push(`Obliteration failed: ${error.message}`);
      return result;
    }
  }

  /**
   * Force cleanup all repeatable jobs (aggressive cleanup)
   */
  async forceCleanupAllRepeatableJobs(): Promise<{
    totalJobs: number;
    removedJobs: number;
    errors: string[];
  }> {
    // First try the standard force cleanup
    const standardResult = await this.standardForceCleanup();

    // If that didn't work, use the nuclear option
    if (standardResult.totalJobs > 0 && standardResult.removedJobs === 0) {
      this.logger.warn('Standard force cleanup failed, using nuclear option...');
      return await this.obliterateAllRepeatableJobs();
    }

    return standardResult;
  }

  /**
   * Standard force cleanup method
   */
  private async standardForceCleanup(): Promise<{
    totalJobs: number;
    removedJobs: number;
    errors: string[];
  }> {
    const result = {
      totalJobs: 0,
      removedJobs: 0,
      errors: [] as string[],
    };

    try {
      // Get all repeatable jobs
      const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
      result.totalJobs = repeatableJobs.length;

      if (repeatableJobs.length === 0) {
        this.logger.log('No repeatable jobs found to force cleanup');
        return result;
      }

      this.logger.log(`Force cleaning ${repeatableJobs.length} repeatable jobs...`);

      for (const job of repeatableJobs) {
        try {
          // Try multiple removal patterns
          const patterns = [
            job.pattern,
            '*/30 * * * *',
            '0,30 * * * *',
          ].filter(Boolean);

          let removed = false;
          for (const pattern of patterns) {
            try {
              removed = await this.autoActionQueue.removeRepeatable(
                job.name,
                { pattern },
                job.id || undefined
              );
              if (removed) {
                result.removedJobs++;
                this.logger.log(`Force removed repeatable job: ${job.name} with pattern: ${pattern}`);
                break;
              }
            } catch (patternError) {
              // Try next pattern
            }
          }

          if (!removed) {
            const errorMsg = `Could not force remove repeatable job: ${job.name}`;
            result.errors.push(errorMsg);
            this.logger.warn(errorMsg);
          }
        } catch (error) {
          const errorMsg = `Error force removing repeatable job ${job.name}: ${error.message}`;
          result.errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      this.logger.log(`Standard force cleanup completed: ${result.removedJobs}/${result.totalJobs} repeatable jobs removed`);
      return result;
    } catch (error) {
      this.logger.error('Failed to force cleanup repeatable jobs', error);
      result.errors.push(`Force cleanup failed: ${error.message}`);
      return result;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats() {
    try {
      const waiting = await this.autoActionQueue.getWaiting();
      const active = await this.autoActionQueue.getActive();
      const completed = await this.autoActionQueue.getCompleted();
      const failed = await this.autoActionQueue.getFailed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        repeatable: 0, // No longer using repeatable jobs
      };
    } catch (error) {
      this.logger.error('Failed to get queue stats', error);
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        repeatable: 0,
      };
    }
  }
}
