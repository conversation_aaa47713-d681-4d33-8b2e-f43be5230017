import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { AutoMode } from '../user/enums/auto-mode.enum';
import { AutoActionJobData, AutoActionJobOptions } from './dto/auto-action-job.dto';

@Injectable()
export class AutoActionQueue {
  private readonly logger = new Logger(AutoActionQueue.name);

  constructor(
    @InjectQueue('auto-actions')
    private readonly autoActionQueue: Queue<AutoActionJobData>,
  ) {}

  /**
   * Add a new auto action job to the queue
   * Uses self-scheduling approach: each job schedules the next one after completion
   */
  async addAutoActionJob(
    userId: number,
    targetId: string,
    type: AutoMode,
    delayMs?: number,
  ): Promise<void> {
    const jobName = `${userId}_${targetId}_${type}`;
    const jobData: AutoActionJobData = {
      userId,
      targetId,
      type,
    };

    // Calculate delay for first execution
    let executionDelay = delayMs;
    if (executionDelay === undefined) {
      // For initial job, schedule 30 minutes from now with small random offset
      // This distributes jobs across time instead of clustering them at fixed intervals
      const baseDelayMs = 30 * 60 * 1000; // 30 minutes
      const randomOffsetMs = Math.floor(Math.random() * 5 * 60 * 1000); // 0-5 minutes
      executionDelay = baseDelayMs + randomOffsetMs;
    }

    const nextExecutionTime = new Date(Date.now() + executionDelay);

    const jobOptions: AutoActionJobOptions = {
      delay: executionDelay,
      removeOnComplete: 10, // Keep last 10 completed jobs
      removeOnFail: 50, // Keep last 50 failed jobs
      attempts: 3, // Retry up to 3 times on failure
      backoff: {
        type: 'exponential',
        delay: 1000, // Start with 1 second delay
      },
    };

    try {
      await this.autoActionQueue.add(jobName, jobData, jobOptions);
      this.logger.log(
        `Added auto action job: ${jobName} - scheduled for ${nextExecutionTime.toISOString()}`
      );
    } catch (error) {
      this.logger.error(`Failed to add auto action job: ${jobName}`, error);
      throw error;
    }
  }

  /**
   * Add a one-time retry job with delay
   */
  async addRetryJob(
    userId: number,
    targetId: string,
    type: AutoMode,
    delayMs: number,
  ): Promise<void> {
    const retryJobName = `${userId}_${targetId}_${type}_retry_${Date.now()}`;
    const jobData: AutoActionJobData = {
      userId,
      targetId,
      type,
    };

    const jobOptions = {
      delay: delayMs, // Delay before execution
      removeOnComplete: 5, // Keep fewer completed retry jobs
      removeOnFail: 10, // Keep fewer failed retry jobs
      attempts: 1, // Don't retry the retry job
    };

    try {
      await this.autoActionQueue.add(retryJobName, jobData, jobOptions);
      this.logger.log(`Added retry job: ${retryJobName} with ${delayMs}ms delay`);
    } catch (error) {
      this.logger.error(`Failed to add retry job: ${retryJobName}`, error);
      throw error;
    }
  }

  /**
   * Remove an auto action job from the queue
   * Since we use self-scheduling, we need to remove all pending jobs with this name
   */
  async removeAutoActionJob(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<void> {
    const jobName = `${userId}_${targetId}_${type}`;

    try {
      // Remove all jobs with this name (waiting, delayed, active)
      const jobs = await this.autoActionQueue.getJobs(['waiting', 'delayed', 'active']);
      let removedJobs = 0;

      for (const job of jobs) {
        try {
          if (job.name === jobName || job.name.startsWith(`${jobName}_retry_`)) {
            await job.remove();
            removedJobs++;
            this.logger.log(`Removed job: ${job.name} (ID: ${job.id})`);
          }
        } catch (jobRemovalError) {
          this.logger.error(`Failed to remove individual job ${job.name}: ${jobRemovalError.message}`);
        }
      }

      this.logger.log(`Removed auto action job: ${jobName} (${removedJobs} jobs removed)`);
    } catch (error) {
      this.logger.error(`Failed to remove auto action job: ${jobName}`, error);
      throw error;
    }
  }

  /**
   * Get all active auto action jobs
   * Since we use self-scheduling, check for waiting/delayed jobs
   */
  async getActiveJobs(): Promise<Array<{ userId: number; targetId: string; type: AutoMode }>> {
    try {
      const jobs = await this.autoActionQueue.getJobs(['waiting', 'delayed']);
      const activeJobs: Array<{ userId: number; targetId: string; type: AutoMode }> = [];

      for (const job of jobs) {
        // Skip retry jobs
        if (job.name.includes('_retry_')) {
          continue;
        }

        const [userId, targetId, type] = job.name.split('_');
        if (userId && targetId && type) {
          activeJobs.push({
            userId: parseInt(userId),
            targetId,
            type: type as AutoMode,
          });
        }
      }

      return activeJobs;
    } catch (error) {
      this.logger.error('Failed to get active jobs', error);
      return [];
    }
  }

  /**
   * Get all active auto action jobs with user validation
   * This method checks if the jobs match the user's current auto mode settings
   */
  async getActiveJobsWithValidation(userService: any): Promise<Array<{
    userId: number;
    targetId: string;
    type: AutoMode;
    isValid: boolean;
    userAutoMode?: AutoMode;
    userTargetId?: string;
  }>> {
    try {
      const jobs = await this.autoActionQueue.getJobs(['waiting', 'delayed']);
      const jobsWithValidation: Array<{
        userId: number;
        targetId: string;
        type: AutoMode;
        isValid: boolean;
        userAutoMode?: AutoMode;
        userTargetId?: string;
      }> = [];

      for (const job of jobs) {
        // Skip retry jobs
        if (job.name.includes('_retry_')) {
          continue;
        }

        const [userId, targetId, type] = job.name.split('_');
        if (!userId || !targetId || !type) {
          continue;
        }

        const parsedUserId = parseInt(userId);

        // Get user's current auto mode settings
        const user = await userService.findOne(parsedUserId);
        const isValid = user &&
          user.activeAutoMode === type &&
          user.autoTargetId === targetId;

        jobsWithValidation.push({
          userId: parsedUserId,
          targetId,
          type: type as AutoMode,
          isValid: !!isValid,
          userAutoMode: user?.activeAutoMode,
          userTargetId: user?.autoTargetId,
        });

        // If job is invalid, log and optionally remove it
        if (!isValid) {
          this.logger.warn(
            `Found inconsistent job: ${job.name}. ` +
            `User auto mode: ${user?.activeAutoMode}, target: ${user?.autoTargetId}. ` +
            `Job will be marked for cleanup.`
          );
        }
      }

      return jobsWithValidation;
    } catch (error) {
      this.logger.error('Failed to get active jobs with validation', error);
      return [];
    }
  }

  /**
   * Clean up inconsistent jobs that don't match user's current auto mode settings
   */
  async cleanupInconsistentJobs(userService: any): Promise<{
    totalJobs: number;
    inconsistentJobs: number;
    cleanedJobs: number;
    errors: string[];
  }> {
    const result = {
      totalJobs: 0,
      inconsistentJobs: 0,
      cleanedJobs: 0,
      errors: [] as string[],
    };

    try {
      const jobsWithValidation = await this.getActiveJobsWithValidation(userService);
      result.totalJobs = jobsWithValidation.length;

      const inconsistentJobs = jobsWithValidation.filter(job => !job.isValid);
      result.inconsistentJobs = inconsistentJobs.length;

      this.logger.log(
        `Found ${result.inconsistentJobs} inconsistent jobs out of ${result.totalJobs} total jobs`
      );

      for (const job of inconsistentJobs) {
        try {
          await this.removeAutoActionJob(job.userId, job.targetId, job.type);
          result.cleanedJobs++;
          this.logger.log(
            `Cleaned up inconsistent job: ${job.userId}_${job.targetId}_${job.type} ` +
            `(user mode: ${job.userAutoMode}, user target: ${job.userTargetId})`
          );
        } catch (error) {
          const errorMsg = `Failed to clean up job ${job.userId}_${job.targetId}_${job.type}: ${error.message}`;
          result.errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      this.logger.log(
        `Cleanup completed: ${result.cleanedJobs}/${result.inconsistentJobs} inconsistent jobs removed`
      );

      return result;
    } catch (error) {
      this.logger.error('Failed to cleanup inconsistent jobs', error);
      result.errors.push(`Cleanup failed: ${error.message}`);
      return result;
    }
  }

  /**
   * Check if a specific auto action job exists
   */
  async jobExists(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<boolean> {
    const jobName = `${userId}_${targetId}_${type}`;

    try {
      // Check for any pending jobs with this name
      const pendingJobs = await this.autoActionQueue.getJobs(['waiting', 'delayed', 'active']);
      const pendingExists = pendingJobs.some(job => job.name === jobName);

      if (pendingExists) {
        this.logger.log(`Found existing job: ${jobName}`);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Failed to check if job exists: ${jobName}`, error);
      return false;
    }
  }

  /**
   * Force remove a job by trying all possible removal methods
   * This is a more aggressive approach for stuck jobs
   */
  async forceRemoveAutoActionJob(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<{ success: boolean; methods: string[] }> {
    const jobName = `${userId}_${targetId}_${type}`;
    const result = { success: false, methods: [] as string[] };

    this.logger.log(`Force removing job: ${jobName}`);

    try {
      // Method 1: Regular removal
      try {
        await this.removeAutoActionJob(userId, targetId, type);
        result.methods.push('regular_removal');
        result.success = true;
        return result;
      } catch (error) {
        this.logger.warn(`Regular removal failed for ${jobName}: ${error.message}`);
      }

      // Method 2: Remove all jobs (waiting, delayed, active) with this name
      try {
        const allJobs = await this.autoActionQueue.getJobs(['waiting', 'delayed', 'active', 'completed', 'failed']);
        let removedCount = 0;
        for (const job of allJobs) {
          if (job.name === jobName || job.name.startsWith(`${jobName}_retry_`)) {
            try {
              await job.remove();
              removedCount++;
            } catch (jobError) {
              // Ignore individual job removal errors
            }
          }
        }
        if (removedCount > 0) {
          result.methods.push(`individual_jobs_${removedCount}`);
          result.success = true;
        }
      } catch (error) {
        this.logger.warn(`Individual job removal failed for ${jobName}: ${error.message}`);
      }

      this.logger.log(`Force removal result for ${jobName}: success=${result.success}, methods=[${result.methods.join(', ')}]`);
      return result;
    } catch (error) {
      this.logger.error(`Force removal failed for ${jobName}`, error);
      return result;
    }
  }

  /**
   * Clean up all travel auto jobs (travel should not be repeating)
   */
  async cleanupTravelAutoJobs(): Promise<{
    totalTravelJobs: number;
    removedJobs: number;
    errors: string[];
  }> {
    const result = {
      totalTravelJobs: 0,
      removedJobs: 0,
      errors: [] as string[],
    };

    try {
      const jobs = await this.autoActionQueue.getJobs(['waiting', 'delayed', 'active']);
      const travelJobs = jobs.filter(job => job.name.endsWith('_travel'));
      result.totalTravelJobs = travelJobs.length;

      this.logger.log(`Found ${result.totalTravelJobs} travel auto jobs to clean up`);

      for (const job of travelJobs) {
        try {
          const [userId, targetId] = job.name.split('_');
          await this.removeAutoActionJob(parseInt(userId), targetId, AutoMode.TRAVEL);
          result.removedJobs++;
          this.logger.log(`Cleaned up travel auto job: ${job.name}`);
        } catch (error) {
          const errorMsg = `Failed to clean up travel job ${job.name}: ${error.message}`;
          result.errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      this.logger.log(
        `Travel auto job cleanup completed: ${result.removedJobs}/${result.totalTravelJobs} jobs removed`
      );

      return result;
    } catch (error) {
      this.logger.error('Failed to cleanup travel auto jobs', error);
      result.errors.push(`Cleanup failed: ${error.message}`);
      return result;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats() {
    try {
      const waiting = await this.autoActionQueue.getWaiting();
      const active = await this.autoActionQueue.getActive();
      const completed = await this.autoActionQueue.getCompleted();
      const failed = await this.autoActionQueue.getFailed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        repeatable: 0, // No longer using repeatable jobs
      };
    } catch (error) {
      this.logger.error('Failed to get queue stats', error);
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        repeatable: 0,
      };
    }
  }
}
